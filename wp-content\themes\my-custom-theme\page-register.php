<?php
/*
Template Name: Custom Register Page
*/

// Redirect if user is already logged in
if (is_user_logged_in()) {
    wp_redirect(home_url());
    exit;
}

// Check if registration is enabled
if (!get_option('users_can_register')) {
    wp_redirect(home_url());
    exit;
}

// Handle registration form submission
if (isset($_POST['register_submit'])) {
    $username = sanitize_text_field($_POST['username']);
    $email = sanitize_email($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    $errors = array();
    
    // Validation
    if (empty($username)) {
        $errors[] = 'Username is required.';
    } elseif (username_exists($username)) {
        $errors[] = 'Username already exists.';
    }
    
    if (empty($email)) {
        $errors[] = 'Email is required.';
    } elseif (!is_email($email)) {
        $errors[] = 'Please enter a valid email address.';
    } elseif (email_exists($email)) {
        $errors[] = 'Email already exists.';
    }
    
    if (empty($password)) {
        $errors[] = 'Password is required.';
    } elseif (strlen($password) < 6) {
        $errors[] = 'Password must be at least 6 characters long.';
    }
    
    if ($password !== $confirm_password) {
        $errors[] = 'Passwords do not match.';
    }
    
    if (empty($errors)) {
        $user_id = wp_create_user($username, $password, $email);
        
        if (is_wp_error($user_id)) {
            $errors[] = $user_id->get_error_message();
        } else {
            // Registration successful
            $success_message = 'Registration successful! You can now login.';
            
            // Auto-login the user (optional)
            $creds = array(
                'user_login'    => $username,
                'user_password' => $password,
                'remember'      => true
            );
            $user = wp_signon($creds, false);
            
            if (!is_wp_error($user)) {
                wp_redirect(home_url());
                exit;
            }
        }
    }
}

get_header(); ?>

<main class="main">
    <!-- breadcrumb -->
    <div class="site-breadcrumb" style="background: url(<?php echo get_template_directory_uri(); ?>/assets/img/breadcrumb/01.jpg)">
        <div class="container">
            <h2 class="breadcrumb-title">Register</h2>
            <ul class="breadcrumb-menu">
                <li><a href="<?php echo home_url(); ?>">Home</a></li>
                <li class="active">Register</li>
            </ul>
        </div>
    </div>
    <!-- breadcrumb end -->

    <!-- register area -->
    <div class="login-area py-120">
        <div class="container">
            <div class="col-md-5 mx-auto">
                <div class="login-form">
                    <div class="login-header">
                        <?php if (has_custom_logo()) : ?>
                            <?php the_custom_logo(); ?>
                        <?php else : ?>
                            <img src="<?php echo get_template_directory_uri(); ?>/assets/img/logo/logo.png" alt="<?php bloginfo('name'); ?>">
                        <?php endif; ?>
                        <p>Create your <?php bloginfo('name'); ?> account</p>
                    </div>

                    <?php if (isset($errors) && !empty($errors)) : ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="far fa-exclamation-triangle"></i>
                            <ul class="mb-0">
                                <?php foreach ($errors as $error) : ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($success_message)) : ?>
                        <div class="alert alert-success" role="alert">
                            <i class="far fa-check-circle"></i> <?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="">
                        <?php wp_nonce_field('custom_register_nonce', 'register_nonce'); ?>
                        
                        <div class="input-group">
                            <span class="input-group-text"><i class="far fa-user"></i></span>
                            <input type="text" name="username" class="form-control" placeholder="Username" required value="<?php echo isset($_POST['username']) ? esc_attr($_POST['username']) : ''; ?>">
                        </div>
                        
                        <div class="input-group">
                            <span class="input-group-text"><i class="far fa-envelope"></i></span>
                            <input type="email" name="email" class="form-control" placeholder="Your Email" required value="<?php echo isset($_POST['email']) ? esc_attr($_POST['email']) : ''; ?>">
                        </div>
                        
                        <div class="input-group">
                            <span class="input-group-text"><i class="far fa-key"></i></span>
                            <input type="password" name="password" class="form-control" placeholder="Password" required>
                        </div>
                        
                        <div class="input-group">
                            <span class="input-group-text"><i class="far fa-key"></i></span>
                            <input type="password" name="confirm_password" class="form-control" placeholder="Confirm Password" required>
                        </div>
                        
                        <div class="d-flex align-items-center">
                            <button type="submit" name="register_submit" class="theme-btn">
                                <span class="far fa-user-plus"></span> Register
                            </button>
                        </div>
                    </form>

                    <div class="login-footer">
                        <p class="mt-20">Already have an account? <a href="<?php echo home_url('/login'); ?>">Login.</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- register area end -->
</main>

<?php get_footer(); ?>
