# WordPress Custom Login Page Setup Guide

## What I've Created

I've created a complete custom login system for your WordPress site using the Eventu design. Here's what's included:

### 📁 Files Created:
1. **`page-login.php`** - Custom login page template
2. **`page-register.php`** - Custom registration page template
3. **Enhanced `functions.php`** - Login/logout functionality and redirects

## 🚀 Setup Instructions

### Step 1: Create WordPress Pages

1. **Login to WordPress Admin** → Go to **Pages** → **Add New**

2. **Create Login Page:**
   - Title: `Login`
   - Slug: `login` (important!)
   - Template: Select **"Custom Login Page"** from the Page Attributes
   - Publish the page

3. **Create Register Page:**
   - Title: `Register`
   - Slug: `register` (important!)
   - Template: Select **"Custom Register Page"** from the Page Attributes
   - Publish the page

### Step 2: Enable User Registration (Optional)

If you want users to be able to register:

1. Go to **Settings** → **General**
2. Check **"Anyone can register"**
3. Set **"New User Default Role"** to **Subscriber** (or your preferred role)
4. Save Changes

### Step 3: Test Your Custom Login

1. **Logout** from WordPress admin
2. Visit: `yoursite.com/login/`
3. You should see your beautiful Eventu-styled login page!

## 🎯 Features Included

### ✅ **Automatic Redirects:**
- `/wp-login.php` → redirects to `/login/`
- Failed login → redirects back to custom login page with error
- After logout → redirects to custom login page
- Already logged in users → redirects to homepage

### ✅ **Security Features:**
- WordPress nonce protection
- Input sanitization
- Proper error handling
- Password strength validation (registration)

### ✅ **User Experience:**
- Beautiful Eventu design integration
- Error messages display
- Remember me functionality
- Forgot password link
- Registration link (if enabled)

### ✅ **Navigation Integration:**
- Automatic Login/Logout menu items
- Proper breadcrumb navigation

## 🔧 Customization Options

### Change Login Page URL
If you want a different URL (like `/signin/`):

1. Change the page slug in WordPress admin
2. Update the functions.php redirects:
```php
$login_page = home_url('/signin/'); // Change this line
```

### Customize After-Login Redirect
Add this to functions.php:
```php
function custom_login_redirect($redirect_to, $request, $user) {
    return home_url('/dashboard/'); // Redirect to custom page
}
add_filter('login_redirect', 'custom_login_redirect', 10, 3);
```

### Add Social Login
The templates include a hook for social login plugins:
```php
do_action('custom_social_login');
```

You can add social login buttons by hooking into this action.

### Customize Error Messages
Modify the error handling in `page-login.php`:
```php
if (is_wp_error($user)) {
    $login_error = 'Custom error message here';
}
```

## 🎨 Styling Customization

The login pages use your existing Eventu CSS. To customize further:

### Add Custom CSS
Add to your theme's `style.css`:
```css
.login-form {
    /* Custom login form styles */
}

.login-header img {
    /* Custom logo styles */
}

.alert {
    /* Custom error/success message styles */
}
```

### Modify Background Image
Change the breadcrumb background in the template files:
```php
<div class="site-breadcrumb" style="background: url(path/to/your/image.jpg)">
```

## 🔒 Security Best Practices

### 1. Hide wp-login.php Completely
Add to functions.php:
```php
function disable_wp_login() {
    global $pagenow;
    if ($pagenow == 'wp-login.php' && !is_admin()) {
        wp_redirect(home_url('/login/'));
        exit;
    }
}
add_action('init', 'disable_wp_login');
```

### 2. Limit Login Attempts
Consider installing a plugin like "Limit Login Attempts Reloaded"

### 3. Two-Factor Authentication
Install a 2FA plugin for additional security

## 📱 Mobile Responsiveness

The login pages are fully responsive using Bootstrap classes from Eventu:
- `col-md-5 mx-auto` - Centers the form
- `input-group` - Responsive input styling
- `d-flex` - Flexible layouts

## 🐛 Troubleshooting

### Login Page Not Working?
1. Check that the page slug is exactly `login`
2. Verify the page template is set to "Custom Login Page"
3. Clear any caching plugins

### Redirects Not Working?
1. Check your permalink settings (Settings → Permalinks)
2. Make sure .htaccess is writable
3. Try flushing permalinks (save permalink settings again)

### Styling Issues?
1. Ensure Eventu CSS is properly loaded
2. Check for theme conflicts
3. Verify image paths are correct

### Registration Not Available?
1. Check Settings → General → "Anyone can register"
2. Verify the register page template is set correctly

## 🚀 Advanced Features

### Custom User Roles
After registration, you can assign custom roles:
```php
// In page-register.php after wp_create_user
$user = new WP_User($user_id);
$user->set_role('custom_role');
```

### Email Verification
Add email verification before account activation:
```php
// Send verification email after registration
wp_new_user_notification($user_id, null, 'user');
```

### Custom Fields
Add custom registration fields by modifying `page-register.php`

## 📊 Analytics Integration

Track login/registration events:
```php
// Add to successful login
if (function_exists('gtag')) {
    echo "<script>gtag('event', 'login', {'method': 'custom_form'});</script>";
}
```

Your custom login system is now ready! Users will have a seamless, branded login experience that matches your Eventu theme perfectly. 🎉
