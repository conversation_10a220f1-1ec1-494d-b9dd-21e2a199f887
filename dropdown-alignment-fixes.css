/* ========================================
   WordPress Dropdown Menu Alignment Fixes
   ======================================== */

/* 1. BASIC DROPDOWN ALIGNMENT - Add this CSS when dropdown exists */
.navbar .nav-item.has-dropdown .dropdown-menu {
    /* Ensure dropdown appears exactly below the parent menu item */
    left: 0 !important;
    right: auto !important;
    margin-top: 0;
    min-width: 220px;
    
    /* Better positioning */
    transform-origin: top center;
    
    /* Smooth animation */
    transition: all 0.3s ease-in-out;
    opacity: 0;
    visibility: hidden;
    top: 100%;
}

/* Show dropdown on hover */
.navbar .nav-item.has-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* 2. RIGHT-ALIGNED DROPDOWNS - For menu items near the right edge */
.navbar .nav-item.dropdown-right .dropdown-menu {
    left: auto !important;
    right: 0 !important;
}

/* 3. CENTER-ALIGNED DROPDOWNS - For better visual balance */
.navbar .nav-item.dropdown-center .dropdown-menu {
    left: 50% !important;
    transform: translateX(-50%);
}

.navbar .nav-item.dropdown-center:hover .dropdown-menu {
    transform: translateX(-50%) translateY(0);
}

/* 4. FULL-WIDTH DROPDOWNS - For mega menus */
.navbar .nav-item.dropdown-fullwidth .dropdown-menu {
    left: 0 !important;
    right: 0 !important;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

/* 5. MULTI-LEVEL DROPDOWN IMPROVEMENTS */
.navbar .nav-item .dropdown-submenu .dropdown-menu {
    /* Position submenu to the right of parent */
    top: 0 !important;
    left: 100% !important;
    margin-left: 5px;
    
    /* Prevent submenu from going off-screen */
    transform: translateX(0);
}

/* Submenu positioning for right-side items */
.navbar .nav-item.dropdown-right .dropdown-submenu .dropdown-menu {
    left: auto !important;
    right: 100% !important;
    margin-left: 0;
    margin-right: 5px;
}

/* 6. RESPONSIVE DROPDOWN ALIGNMENT */
@media (max-width: 991px) {
    .navbar .nav-item .dropdown-menu {
        position: static !important;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;
        transform: none !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    .navbar .nav-item .dropdown-submenu .dropdown-menu {
        margin-left: 20px;
        position: static !important;
    }
}

/* 7. DROPDOWN ARROW INDICATORS */
.navbar .nav-item.has-dropdown > .nav-link::after {
    content: "\f107";
    font-family: 'Font Awesome 6 Pro';
    margin-left: 8px;
    transition: transform 0.3s ease;
}

.navbar .nav-item.has-dropdown:hover > .nav-link::after {
    transform: rotate(180deg);
}

/* 8. PREVENT DROPDOWN OVERFLOW */
.navbar .nav-item .dropdown-menu {
    /* Ensure dropdown doesn't go beyond viewport */
    max-width: calc(100vw - 40px);
    overflow: hidden;
}

/* Auto-adjust dropdown position if it would overflow */
.navbar .nav-item:nth-last-child(-n+2) .dropdown-menu {
    left: auto !important;
    right: 0 !important;
}

/* 9. ENHANCED VISUAL STYLING */
.navbar .nav-item .dropdown-menu {
    /* Better visual appearance */
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: 8px;
    padding: 8px 0;
    background: #ffffff;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    
    /* Prevent text selection issues */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.navbar .nav-item .dropdown-item {
    padding: 8px 20px;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    transition: all 0.2s ease;
}

.navbar .nav-item .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #007bff;
    padding-left: 25px;
}

/* 10. ACCESSIBILITY IMPROVEMENTS */
.navbar .nav-item .dropdown-menu {
    /* Better focus management */
    outline: none;
}

.navbar .nav-item .dropdown-item:focus {
    background-color: #e9ecef;
    outline: 2px solid #007bff;
    outline-offset: -2px;
}

/* 11. WORDPRESS SPECIFIC CLASSES */
/* Add these classes to your WordPress menu items */
.menu-item-has-children > .dropdown-menu {
    /* WordPress menu item with children */
    display: block;
}

.current-menu-item > .nav-link,
.current-menu-ancestor > .nav-link {
    /* Highlight current page */
    color: #007bff !important;
}

/* 12. ANIMATION VARIANTS */
/* Fade in animation */
.dropdown-fade .dropdown-menu {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown-fade:hover .dropdown-menu {
    opacity: 1;
    transform: translateY(0);
}

/* Slide down animation */
.dropdown-slide .dropdown-menu {
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.dropdown-slide:hover .dropdown-menu {
    opacity: 1;
    transform: translateY(0);
}

/* Scale animation */
.dropdown-scale .dropdown-menu {
    opacity: 0;
    transform: scale(0.95);
    transition: all 0.2s ease;
}

.dropdown-scale:hover .dropdown-menu {
    opacity: 1;
    transform: scale(1);
}
