<?php
/*
Template Name: Custom Login Page
*/

// Redirect if user is already logged in
if (is_user_logged_in()) {
    wp_redirect(home_url());
    exit;
}

// Handle login form submission
if (isset($_POST['login_submit'])) {
    $username = sanitize_text_field($_POST['username']);
    $password = $_POST['password'];
    $remember = isset($_POST['remember']) ? true : false;
    
    $creds = array(
        'user_login'    => $username,
        'user_password' => $password,
        'remember'      => $remember
    );
    
    $user = wp_signon($creds, false);
    
    if (is_wp_error($user)) {
        $login_error = $user->get_error_message();
    } else {
        // Successful login - redirect
        $redirect_to = isset($_POST['redirect_to']) ? $_POST['redirect_to'] : home_url();
        wp_redirect($redirect_to);
        exit;
    }
}

get_header(); ?>

<main class="main">
    <!-- breadcrumb -->
    <div class="site-breadcrumb" style="background: url(<?php echo get_template_directory_uri(); ?>/assets/img/breadcrumb/01.jpg)">
        <div class="container">
            <h2 class="breadcrumb-title">Login</h2>
            <ul class="breadcrumb-menu">
                <li><a href="<?php echo home_url(); ?>">Home</a></li>
                <li class="active">Login</li>
            </ul>
        </div>
    </div>
    <!-- breadcrumb end -->

    <!-- login area -->
    <div class="login-area py-120">
        <div class="container">
            <div class="col-md-5 mx-auto">
                <div class="login-form">
                    <div class="login-header">
                        <?php if (has_custom_logo()) : ?>
                            <?php the_custom_logo(); ?>
                        <?php else : ?>
                            <img src="<?php echo get_template_directory_uri(); ?>/assets/img/logo/logo.png" alt="<?php bloginfo('name'); ?>">
                        <?php endif; ?>
                        <p>Login with your <?php bloginfo('name'); ?> account</p>
                    </div>

                    <?php if (isset($login_error)) : ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="far fa-exclamation-triangle"></i> <?php echo $login_error; ?>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="">
                        <?php wp_nonce_field('custom_login_nonce', 'login_nonce'); ?>
                        
                        <div class="input-group">
                            <span class="input-group-text"><i class="far fa-envelope"></i></span>
                            <input type="text" name="username" class="form-control" placeholder="Username or Email" required value="<?php echo isset($_POST['username']) ? esc_attr($_POST['username']) : ''; ?>">
                        </div>
                        
                        <div class="input-group">
                            <span class="input-group-text"><i class="far fa-key"></i></span>
                            <input type="password" name="password" class="form-control" placeholder="Your Password" required>
                        </div>
                        
                        <div class="d-flex justify-content-between mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="remember" value="1" id="remember">
                                <label class="form-check-label" for="remember">
                                    Remember Me
                                </label>
                            </div>
                            <a href="<?php echo home_url('/forgot-password/'); ?>" class="forgot-password">Forgot Password?</a>
                        </div>
                        
                        <input type="hidden" name="redirect_to" value="<?php echo isset($_GET['redirect_to']) ? esc_url($_GET['redirect_to']) : home_url(); ?>">
                        
                        <div class="d-flex align-items-center">
                            <button type="submit" name="login_submit" class="theme-btn">
                                <span class="far fa-sign-in"></span> Login
                            </button>
                        </div>
                    </form>

                    <div class="login-footer">
                        <?php if (get_option('users_can_register')) : ?>
                            <p class="mt-20">Don't have an account? <a href="<?php echo wp_registration_url(); ?>">Register.</a></p>
                        <?php endif; ?>
                        
                        <?php
                        // Social login integration (if you have social login plugins)
                        do_action('custom_social_login');
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- login area end -->
</main>

<?php get_footer(); ?>
