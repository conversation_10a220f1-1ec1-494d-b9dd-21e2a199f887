<?php
class Bootstra<PERSON>_NavWalker extends Walker_Nav_Menu {

    // Track which items have children
    public $has_children = array();

    // Start Level - for dropdown menus
    function start_lvl(&$output, $depth = 0, $args = null) {
        $indent = str_repeat("\t", $depth);
        $output .= "\n$indent<ul class=\"dropdown-menu fade-down\">\n";
    }

    // End Level
    function end_lvl(&$output, $depth = 0, $args = null) {
        $indent = str_repeat("\t", $depth);
        $output .= "$indent</ul>\n";
    }

    // Start Element
    function start_el(&$output, $item, $depth = 0, $args = array(), $id = 0) {
        $indent = ($depth) ? str_repeat("\t", $depth) : '';

        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $classes[] = 'nav-item';

        // Check if this item has children
        $has_children = in_array('menu-item-has-children', $classes);

        if ($has_children) {
            $classes[] = 'dropdown';
            $classes[] = 'has-dropdown';
        }

        // For submenu items
        if ($depth > 0) {
            $classes = array_diff($classes, array('nav-item', 'dropdown', 'has-dropdown'));
            if ($has_children) {
                $classes[] = 'dropdown-submenu';
            }
        }

        $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

        $id = apply_filters('nav_menu_item_id', 'menu-item-'. $item->ID, $item, $args);
        $id = $id ? ' id="' . esc_attr($id) . '"' : '';

        $output .= $indent . '<li' . $id . $class_names .'>';

        // Build the link
        $link_classes = array();
        if ($depth == 0) {
            $link_classes[] = 'nav-link';
        } else {
            $link_classes[] = 'dropdown-item';
        }

        if ($has_children) {
            $link_classes[] = 'dropdown-toggle';
        }

        $attributes = ' class="' . implode(' ', $link_classes) . '"';
        $attributes .= !empty($item->url) ? ' href="' . esc_attr($item->url) . '"' : '';

        // Add Bootstrap dropdown attributes for items with children
        if ($has_children && $depth == 0) {
            $attributes .= ' data-bs-toggle="dropdown" role="button" aria-expanded="false"';
        }

        $item_output = isset($args->before) ? $args->before : '';
        $item_output .= '<a' . $attributes . '>';
        $item_output .= (isset($args->link_before) ? $args->link_before : '') . apply_filters('the_title', $item->title, $item->ID) . (isset($args->link_after) ? $args->link_after : '');
        $item_output .= '</a>';
        $item_output .= isset($args->after) ? $args->after : '';

        $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
    }

    // End Element
    function end_el(&$output, $item, $depth = 0, $args = array()) {
        $output .= "</li>\n";
    }
}

?>
